package main

import (
	"crypto/sha256"
	"crypto/subtle"
	"log"
	"os"

	"github.com/applegold/surgassists-ar-backend/routes"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/keyauth"
	"github.com/joho/godotenv"
)

func setUpRoutes(app *fiber.App, apiKey string) {

	authMiddleware := keyauth.New(keyauth.Config{
		Validator: func(c *fiber.Ctx, key string) (bool, error) {
			hashedAPIKey := sha256.Sum256([]byte(apiKey))
			hashedKey := sha256.Sum256([]byte(key))

			if subtle.ConstantTimeCompare(hashedAPIKey[:], hashedKey[:]) == 1 {
				return true, nil
			}
			return false, keyauth.ErrMissingOrMalformedAPIKey
		},
	})

	app.Get("/", routes.Hello)

	app.Get("/allowed", authMiddleware, routes.Allowed)

	app.Post("/start-patient-consent", authMiddleware, routes.StartPatientConsent)

	app.Post("/log-stats", authMiddleware, routes.LogStats)

	app.Get("/patient-consent-viewed/:id", authMiddleware, routes.PatientConsentViewed)

	app.Get("/make-qr-code/:id", authMiddleware, routes.MakeQRCode)

	app.Get("/unity-ar-status/:id", authMiddleware, routes.UnityARStatus)

}

func main() {
	// Load environment variables from .env file
	err := godotenv.Load()
	if err != nil {
		log.Fatal("Error loading .env file")
	}

	// Get API key from environment variable
	apiKey := os.Getenv("API_KEY")
	if apiKey == "" {
		log.Fatal("API_KEY environment variable is required")
	}

	app := fiber.New()

	setUpRoutes(app, apiKey)

	log.Fatal(app.Listen("0.0.0.0:6008"))
}
