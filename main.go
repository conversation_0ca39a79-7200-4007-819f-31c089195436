package main

import (
	"crypto/sha256"
	"crypto/subtle"
	"log"

	"github.com/applegold/surgassists-ar-backend/routes"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/keyauth"
)

const (
	apiKey = "NzssOajXMGoFK1gQOjS0MNDejDp0V9v4Jhj7JqQQD1A1A5XTtpw6hfd2TIzxruqE"
)

func setUpRoutes(app *fiber.App) {

	authMiddleware := keyauth.New(keyauth.Config{
		Validator: func(c *fiber.Ctx, key string) (bool, error) {
			hashedAPIKey := sha256.Sum256([]byte(apiKey))
			hashedKey := sha256.Sum256([]byte(key))

			if subtle.ConstantTimeCompare(hashedAPIKey[:], hashedKey[:]) == 1 {
				return true, nil
			}
			return false, keyauth.ErrMissingOrMalformedAPIKey
		},
	})

	app.Get("/", routes.Hello)

	app.Get("/allowed", authMiddleware, routes.Allowed)
}

func main() {

	app := fiber.New()

	setUpRoutes(app)

	log.Fatal(app.Listen(":3000"))
}
