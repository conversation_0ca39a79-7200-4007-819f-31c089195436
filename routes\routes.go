package routes

import (
	"github.com/gofiber/fiber/v2"
)

func Hello(c *fiber.Ctx) error {

	return c.SendString("Hello, World!")
}

func Allowed(c *fiber.Ctx) error {
	return c.SendString("Successfully authenticated!")
}

func StartPatientConsent(c *fiber.Ctx) error {
	return c.SendString("Starting patient consent...")
}

func LogStats(c *fiber.Ctx) error {
	return c.SendString("Logging stats...")
}
