package routes

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"

	"github.com/gofiber/fiber/v2"
)

// PatientConsentRequest represents the incoming JSON payload
type PatientConsentRequest struct {
	ID        string `json:"$id"`
	UserID    string `json:"UserID"`
	Email     string `json:"Email"`
	Status    string `json:"Status"`
	CreatedAt string `json:"$createdAt"`
}

// PostalEmailRequest represents the request structure for Postal API
type PostalEmailRequest struct {
	To        []string `json:"to"`
	From      string   `json:"from"`
	Subject   string   `json:"subject"`
	PlainBody string   `json:"plain_body"`
}

// sendEmail sends an email using the Postal mail service
func sendEmail(toEmail, patientID string) error {
	mailServerAPIKey := os.Getenv("MAIL_SERVER_API_KEY")
	mailServiceURL := os.Getenv("MAIL_SERVICE_URL")

	if mailServerAPIKey == "" || mailServiceURL == "" {
		return fmt.Errorf("mail server configuration missing")
	}

	// Create the email content
	subject := "Medical AR App - Content Request"
	body := fmt.Sprintf("We are reaching out via email to request content for a medical AR app.\n\nPlease visit: https://surgassists-connect.online/patient/%s", patientID)

	emailRequest := PostalEmailRequest{
		To:        []string{toEmail},
		From:      "<EMAIL>", // You may want to make this configurable
		Subject:   subject,
		PlainBody: body,
	}

	// Convert to JSON
	jsonData, err := json.Marshal(emailRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal email request: %v", err)
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", mailServiceURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Server-API-Key", mailServerAPIKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send email: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("email service returned status: %d", resp.StatusCode)
	}

	log.Printf("Email sent successfully to %s", toEmail)
	return nil
}

func Hello(c *fiber.Ctx) error {

	return c.SendString("Hello, World!")
}

func Allowed(c *fiber.Ctx) error {
	return c.SendString("Successfully authenticated!")
}

func StartPatientConsent(c *fiber.Ctx) error {
	var request PatientConsentRequest

	// Parse the JSON body
	if err := c.BodyParser(&request); err != nil {
		log.Printf("Error parsing request body: %v", err)
		return c.Status(400).JSON(fiber.Map{
			"error": "Invalid JSON payload",
		})
	}

	// Log the received data
	log.Printf("Received patient consent request:")
	log.Printf("  ID: %s", request.ID)
	log.Printf("  UserID: %s", request.UserID)
	log.Printf("  Email: %s", request.Email)
	log.Printf("  Status: %s", request.Status)
	log.Printf("  CreatedAt: %s", request.CreatedAt)

	// Send email to the patient
	if err := sendEmail(request.Email, request.ID); err != nil {
		log.Printf("Failed to send email to %s: %v", request.Email, err)
		return c.Status(500).JSON(fiber.Map{
			"error":   "Failed to send email",
			"message": "Patient consent request received but email sending failed",
			"data":    request,
		})
	}

	log.Printf("Email sent successfully to %s for patient ID: %s", request.Email, request.ID)

	//here 


	return c.JSON(fiber.Map{
		"message":    "Patient consent request received, logged, and email sent successfully",
		"data":       request,
		"email_sent": true,
	})
}

func LogStats(c *fiber.Ctx) error {
	return c.SendString("Logging stats...")
}
