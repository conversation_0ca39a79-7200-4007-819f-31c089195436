package routes

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"

	"github.com/gofiber/fiber/v2"
	"github.com/skip2/go-qrcode"
)

// PatientConsentRequest represents the incoming JSON payload
type PatientConsentRequest struct {
	ID        string `json:"$id"`
	UserID    string `json:"UserID"`
	Email     string `json:"Email"`
	Status    string `json:"Status"`
	CreatedAt string `json:"$createdAt"`
}

// PostalEmailRequest represents the request structure for Postal API
type PostalEmailRequest struct {
	To        []string `json:"to"`
	From      string   `json:"from"`
	Subject   string   `json:"subject"`
	PlainBody string   `json:"plain_body"`
}

// AppwriteUpdateRequest represents the request structure for updating Appwrite document
type AppwriteUpdateRequest struct {
	Data map[string]interface{} `json:"data"`
}

// getAppwriteDocument retrieves a document from Appwrite database
func getAppwriteDocument(documentID string) (map[string]interface{}, error) {
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	consentCollection := os.Getenv("CONSENT_COLLECTION")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")

	if appwriteKey == "" || appwriteEndpoint == "" || consentCollection == "" {
		return nil, fmt.Errorf("appwrite configuration missing")
	}

	// Use default values if not specified
	if appwriteProjectID == "" {
		appwriteProjectID = "default"
	}
	if appwriteDatabaseID == "" {
		appwriteDatabaseID = "default"
	}

	// Build the URL
	url := fmt.Sprintf("%s/databases/%s/collections/%s/documents/%s",
		appwriteEndpoint, appwriteDatabaseID, consentCollection, documentID)

	// Create HTTP request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create appwrite request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Log the request details for debugging
	log.Printf("=== APPWRITE GET REQUEST ===")
	log.Printf("Full URL: %s", url)
	log.Printf("Method: GET")
	log.Printf("Headers:")
	log.Printf("  Content-Type: application/json")
	log.Printf("  X-Appwrite-Project: %s", appwriteProjectID)
	log.Printf("  X-Appwrite-Key: %s", appwriteKey)
	log.Printf("============================")

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get appwrite document: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	var responseBody map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&responseBody); err != nil {
		return nil, fmt.Errorf("failed to decode response: %v", err)
	}

	log.Printf("Appwrite response status: %d", resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		log.Printf("Appwrite error response: %v", responseBody)
		return nil, fmt.Errorf("appwrite service returned status: %d", resp.StatusCode)
	}

	log.Printf("Appwrite document retrieved successfully for ID: %s", documentID)
	return responseBody, nil
}

// updateAppwriteDocumentStatus updates the document status in Appwrite database with a custom status
func updateAppwriteDocumentStatus(documentID, status string) error {
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	consentCollection := os.Getenv("CONSENT_COLLECTION")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")

	if appwriteKey == "" || appwriteEndpoint == "" || consentCollection == "" {
		return fmt.Errorf("appwrite configuration missing")
	}

	// Use default values if not specified
	if appwriteProjectID == "" {
		appwriteProjectID = "default"
	}
	if appwriteDatabaseID == "" {
		appwriteDatabaseID = "default"
	}

	// Create the update request
	updateRequest := AppwriteUpdateRequest{
		Data: map[string]interface{}{
			"Status": status,
		},
	}

	// Convert to JSON
	jsonData, err := json.Marshal(updateRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal appwrite update request: %v", err)
	}

	// Build the URL
	url := fmt.Sprintf("%s/databases/%s/collections/%s/documents/%s",
		appwriteEndpoint, appwriteDatabaseID, consentCollection, documentID)

	// Create HTTP request
	req, err := http.NewRequest("PATCH", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create appwrite request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Log the request details for debugging
	log.Printf("=== APPWRITE UPDATE REQUEST ===")
	log.Printf("Full URL: %s", url)
	log.Printf("Method: PATCH")
	log.Printf("Headers:")
	log.Printf("  Content-Type: application/json")
	log.Printf("  X-Appwrite-Project: %s", appwriteProjectID)
	log.Printf("  X-Appwrite-Key: %s", appwriteKey)
	log.Printf("Request Body: %s", string(jsonData))
	log.Printf("===============================")

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to update appwrite document: %v", err)
	}
	defer resp.Body.Close()

	// Read response body for better error debugging
	body, _ := json.Marshal(resp.Body)
	log.Printf("Appwrite response status: %d", resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		log.Printf("Appwrite error response: %s", string(body))
		return fmt.Errorf("appwrite service returned status: %d", resp.StatusCode)
	}

	log.Printf("Appwrite document %s updated successfully with status '%s'", documentID, status)
	return nil
}

// updateAppwriteDocument updates the document status in Appwrite database to "Email Sent"
func updateAppwriteDocument(documentID string) error {
	return updateAppwriteDocumentStatus(documentID, "Email Sent")
}

// sendEmail sends an email using the Postal mail service
func sendEmail(toEmail, subject, body string) error {
	mailServerAPIKey := os.Getenv("MAIL_SERVER_API_KEY")
	mailServiceURL := os.Getenv("MAIL_SERVICE_URL")

	if mailServerAPIKey == "" || mailServiceURL == "" {
		return fmt.Errorf("mail server configuration missing")
	}

	emailRequest := PostalEmailRequest{
		To:        []string{toEmail},
		From:      "<EMAIL>", // You may want to make this configurable
		Subject:   subject,
		PlainBody: body,
	}

	// Convert to JSON
	jsonData, err := json.Marshal(emailRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal email request: %v", err)
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", mailServiceURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Server-API-Key", mailServerAPIKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send email: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("email service returned status: %d", resp.StatusCode)
	}

	log.Printf("Email sent successfully to %s", toEmail)
	return nil
}

func Hello(c *fiber.Ctx) error {

	return c.SendString("Hello, World!")
}

func Allowed(c *fiber.Ctx) error {
	return c.SendString("Successfully authenticated!")
}

func StartPatientConsent(c *fiber.Ctx) error {
	var request PatientConsentRequest

	// Parse the JSON body
	if err := c.BodyParser(&request); err != nil {
		log.Printf("Error parsing request body: %v", err)
		return c.Status(400).JSON(fiber.Map{
			"error": "Invalid JSON payload",
		})
	}

	// Log the received data
	log.Printf("Received patient consent request:")
	log.Printf("  ID: %s", request.ID)
	log.Printf("  UserID: %s", request.UserID)
	log.Printf("  Email: %s", request.Email)
	log.Printf("  Status: %s", request.Status)
	log.Printf("  CreatedAt: %s", request.CreatedAt)

	// Send email to the patient
	patientPortalBaseURL := os.Getenv("PATIENT_PORTAL_BASE_URL")
	if patientPortalBaseURL == "" {
		patientPortalBaseURL = "https://surgassists-connect.online"
	}

	// Original consent email content
	subject := "Medical AR App - Content Request"
	body := fmt.Sprintf("We are reaching out via email to request content for a medical AR app.\n\nPlease visit: %s/patient/%s", patientPortalBaseURL, request.ID)

	if err := sendEmail(request.Email, subject, body); err != nil {
		log.Printf("Failed to send email to %s: %v", request.Email, err)
		return c.Status(500).JSON(fiber.Map{
			"error":   "Failed to send email",
			"message": "Patient consent request received but email sending failed",
			"data":    request,
		})
	}

	log.Printf("Email sent successfully to %s for patient ID: %s", request.Email, request.ID)

	// Update Appwrite document status to "Email Sent"
	if err := updateAppwriteDocument(request.ID); err != nil {
		log.Printf("Failed to update Appwrite document %s: %v", request.ID, err)
		return c.Status(500).JSON(fiber.Map{
			"error":      "Failed to update database",
			"message":    "Email sent successfully but database update failed",
			"data":       request,
			"email_sent": true,
			"db_updated": false,
		})
	}

	log.Printf("Appwrite document updated successfully for patient ID: %s", request.ID)

	return c.JSON(fiber.Map{
		"message":    "Patient consent request received, logged, email sent, and database updated successfully",
		"data":       request,
		"email_sent": true,
		"db_updated": true,
	})
}

func LogStats(c *fiber.Ctx) error {
	return c.SendString("Logging stats...")
}

func PatientConsentViewed(c *fiber.Ctx) error {
	// Get the ID from the URL parameter
	patientID := c.Params("id")

	if patientID == "" {
		log.Printf("Missing patient ID in URL")
		return c.Status(400).JSON(fiber.Map{
			"error": "Patient ID is required",
		})
	}

	log.Printf("Patient consent viewed for ID: %s", patientID)

	// Get the document from Appwrite
	document, err := getAppwriteDocument(patientID)
	if err != nil {
		log.Printf("Failed to retrieve document for ID %s: %v", patientID, err)
		return c.Status(500).JSON(fiber.Map{
			"error":      "Failed to retrieve patient information",
			"patient_id": patientID,
		})
	}

	// Update Appwrite document status to "Patient Open Form"
	if err := updateAppwriteDocumentStatus(patientID, "Patient Open Form"); err != nil {
		log.Printf("Failed to update document status for ID %s: %v", patientID, err)
		// Don't return error here, just log it - we still want to return the document
		return c.JSON(fiber.Map{
			"message":        "Patient consent viewed successfully",
			"patient_id":     patientID,
			"document":       document,
			"status_updated": false,
		})
	} else {
		log.Printf("Document status updated to 'Patient Open Form' for ID: %s", patientID)
	}

	return c.JSON(fiber.Map{
		"message":        "Patient consent viewed successfully",
		"patient_id":     patientID,
		"document":       document,
		"status_updated": true,
	})
}

func MakeQRCode(c *fiber.Ctx) error {
	// Get the ID from the URL parameter
	patientID := c.Params("id")

	if patientID == "" {
		log.Printf("Missing patient ID in URL for QR code generation")
		return c.Status(400).JSON(fiber.Map{
			"error": "Patient ID is required",
		})
	}

	log.Printf("Checking document exists for patient ID: %s", patientID)

	// Check if the document exists in Appwrite before generating QR code
	_, err := getAppwriteDocument(patientID)
	if err != nil {
		log.Printf("Failed to retrieve document for ID %s: %v", patientID, err)
		return c.Status(404).JSON(fiber.Map{
			"error":      "Patient document not found",
			"patient_id": patientID,
		})
	}

	log.Printf("Document found, updating status to 'QR Generated' for patient ID: %s", patientID)

	// Update Appwrite document status to "QR Generated"
	if err := updateAppwriteDocumentStatus(patientID, "QR Generated"); err != nil {
		log.Printf("Failed to update document status for ID %s: %v", patientID, err)
		return c.Status(500).JSON(fiber.Map{
			"error":      "Failed to update document status",
			"patient_id": patientID,
		})
	}

	log.Printf("Status updated, generating QR code for patient ID: %s", patientID)

	// Generate QR code containing just the patient ID (for AR app scanning)
	qrCodeBytes, err := qrcode.Encode(patientID, qrcode.Medium, 256)
	if err != nil {
		log.Printf("Failed to generate QR code for ID %s: %v", patientID, err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to generate QR code",
		})
	}

	// Convert to base64 for JSON response (useful for email embedding)
	qrCodeBase64 := base64.StdEncoding.EncodeToString(qrCodeBytes)

	log.Printf("QR code generated successfully for patient ID: %s", patientID)

	return c.JSON(fiber.Map{
		"message":         "QR code generated successfully",
		"patient_id":      patientID,
		"qr_code_content": patientID,
		"qr_code_base64":  qrCodeBase64,
		"qr_code_size":    len(qrCodeBytes),
	})
}

func UnityARStatus(c *fiber.Ctx) error {

	// Get the ID from the URL parameter
	patientID := c.Params("id")

	if patientID == "" {
		log.Printf("Missing patient ID in URL for Unity AR status update")
		return c.Status(400).JSON(fiber.Map{
			"error": "Patient ID is required",
		})
	}

	log.Printf("Checking Unity AR status for patient ID: %s", patientID)

	// Check if the document exists in Appwrite
	document, err := getAppwriteDocument(patientID)
	if err != nil {
		log.Printf("Failed to retrieve document for ID %s: %v", patientID, err)
		return c.Status(404).JSON(fiber.Map{
			"error":      "Patient document not found",
			"patient_id": patientID,
		})
	}

	log.Printf("Document found for patient ID: %s", patientID)

	return c.JSON(fiber.Map{
		"message":    "Unity AR status updated successfully",
		"patient_id": patientID,
		"document":   document,
	})

}

func PatientStartedSignature(c *fiber.Ctx) error {
	// Get the ID from the URL parameter
	patientID := c.Params("id")

	if patientID == "" {
		log.Printf("Missing patient ID in URL for patient started signature")
		return c.Status(400).JSON(fiber.Map{
			"error": "Patient ID is required",
		})
	}

	log.Printf("Checking patient started signature for ID: %s", patientID)

	// Check if the document exists in Appwrite
	document, err := getAppwriteDocument(patientID)
	if err != nil {
		log.Printf("Failed to retrieve document for ID %s: %v", patientID, err)
		return c.Status(404).JSON(fiber.Map{
			"error":      "Patient document not found",
			"patient_id": patientID,
		})
	}

	// Extract email from the document
	var email string
	if emailValue, exists := document["Email"]; exists {
		if emailStr, ok := emailValue.(string); ok {
			email = emailStr
		}
	}

	// Start Document sign using documenso
	// Send email to the patient with document signing link
	docToSignURL := os.Getenv("DOC_TO_SIGN")
	if docToSignURL == "" {
		log.Printf("DOC_TO_SIGN environment variable not set")
		return c.Status(500).JSON(fiber.Map{
			"error": "Document signing URL not configured",
		})
	}

	// Create document signing email content
	subject := "You have a document to sign"
	body := fmt.Sprintf("Dear Patient,\n\nYou have a document that requires your signature.\n\nPlease click the link below to sign your document:\n%s\n\nThank you,\nSurgAssists Team", docToSignURL)

	// Send the email
	if err := sendEmail(email, subject, body); err != nil {
		log.Printf("Failed to send document signing email to %s: %v", email, err)
		return c.Status(500).JSON(fiber.Map{
			"error":      "Failed to send document signing email",
			"patient_id": patientID,
		})
	}

	log.Printf("Document signing email sent successfully to %s", email)

	log.Printf("Document found for patient ID: %s, email: %s", patientID, email)

	// Update Appwrite document status to "Patient Started Signature"
	if err := updateAppwriteDocumentStatus(patientID, "Patient Started Signature"); err != nil {
		log.Printf("Failed to update document status for ID %s: %v", patientID, err)
		return c.Status(500).JSON(fiber.Map{
			"error":      "Failed to update document status",
			"patient_id": patientID,
		})
	}

	log.Printf("Document status updated to 'Patient Started Signature' for patient ID: %s", patientID)

	return c.JSON(fiber.Map{
		"message":    "Patient started signature successfully",
		"patient_id": patientID,
		"document":   document,
	})
}

func DocumensoDone(c *fiber.Ctx) error {
	// Get the ID from the URL parameter

	log.Printf("=== DOCUMENSO WEBHOOK RECEIVED ===")

	// Validate webhook secret
	webDocHook := os.Getenv("WEB_DOC_HOOK")
	if webDocHook == "" {
		log.Printf("WEB_DOC_HOOK environment variable not set")
		return c.Status(500).JSON(fiber.Map{
			"error": "Webhook configuration missing",
		})
	}

	documensoSecret := c.Get("X-Documenso-Secret")
	if documensoSecret != webDocHook {
		log.Printf("Invalid webhook secret. Expected: %s, Got: %s", webDocHook, documensoSecret)
		return c.Status(401).JSON(fiber.Map{
			"error": "No Access",
		})
	}

	log.Printf("Webhook secret validated successfully")

	// Log all headers
	log.Printf("Headers received from Documenso:")
	for key, values := range c.GetReqHeaders() {
		for _, value := range values {
			log.Printf("  %s: %s", key, value)
		}
	}

	// Log request method and URL
	log.Printf("Method: %s", c.Method())
	log.Printf("URL: %s", c.OriginalURL())
	log.Printf("Remote IP: %s", c.IP())

	// Log body if present
	body := c.Body()
	if len(body) > 0 {
		log.Printf("Body: %s", string(body))
	} else {
		log.Printf("Body: (empty)")
		log.Printf("=== END DOCUMENSO WEBHOOK ===")
		return c.JSON(fiber.Map{
			"message": "Documenso webhook received but no body",
		})
	}

	// Parse the JSON body
	var webhookData map[string]interface{}
	if err := json.Unmarshal(body, &webhookData); err != nil {
		log.Printf("Failed to parse webhook JSON: %v", err)
		log.Printf("=== END DOCUMENSO WEBHOOK ===")
		return c.Status(400).JSON(fiber.Map{
			"error": "Invalid JSON payload",
		})
	}

	// Check if this is a DOCUMENT_COMPLETED event
	event, exists := webhookData["event"]
	if !exists || event != "DOCUMENT_COMPLETED" {
		log.Printf("Event is not DOCUMENT_COMPLETED, ignoring webhook")
		log.Printf("=== END DOCUMENSO WEBHOOK ===")
		return c.JSON(fiber.Map{
			"message": "Event ignored - not DOCUMENT_COMPLETED",
		})
	}

	log.Printf("Processing DOCUMENT_COMPLETED event")

	// Extract payload
	payload, exists := webhookData["payload"]
	if !exists {
		log.Printf("No payload found in webhook")
		log.Printf("=== END DOCUMENSO WEBHOOK ===")
		return c.Status(400).JSON(fiber.Map{
			"error": "No payload in webhook",
		})
	}

	payloadMap, ok := payload.(map[string]interface{})
	if !ok {
		log.Printf("Payload is not a valid object")
		log.Printf("=== END DOCUMENSO WEBHOOK ===")
		return c.Status(400).JSON(fiber.Map{
			"error": "Invalid payload format",
		})
	}

	// Extract email from recipients
	var email string
	var documentDataId string
	var status string

	// Get documentDataId and status
	if docDataId, exists := payloadMap["documentDataId"]; exists {
		if docDataIdStr, ok := docDataId.(string); ok {
			documentDataId = docDataIdStr
		}
	}

	if statusVal, exists := payloadMap["status"]; exists {
		if statusStr, ok := statusVal.(string); ok {
			status = statusStr
		}
	}

	// Get email from recipients array
	if recipients, exists := payloadMap["recipients"]; exists {
		if recipientsArray, ok := recipients.([]interface{}); ok && len(recipientsArray) > 0 {
			if recipient, ok := recipientsArray[0].(map[string]interface{}); ok {
				if emailVal, exists := recipient["email"]; exists {
					if emailStr, ok := emailVal.(string); ok {
						email = emailStr
					}
				}
			}
		}
	}

	log.Printf("Extracted data - Email: %s, DocumentDataId: %s, Status: %s", email, documentDataId, status)

	if email == "" || documentDataId == "" {
		log.Printf("Missing required data from webhook")
		log.Printf("=== END DOCUMENSO WEBHOOK ===")
		return c.Status(400).JSON(fiber.Map{
			"error": "Missing email or documentDataId in webhook",
		})
	}

	// Look up document ID from Appwrite using email
	log.Printf("Looking up Appwrite document by email: %s", email)
	patientID, err := getAppwriteDocumentByEmail(email)
	if err != nil {
		log.Printf("Failed to find document by email %s: %v", email, err)
		log.Printf("=== END DOCUMENSO WEBHOOK ===")
		return c.Status(404).JSON(fiber.Map{
			"error": "Patient document not found by email",
		})
	}

	log.Printf("Found patient ID: %s for email: %s", patientID, email)

	// Send QR code email
	patientPortalBaseURL := os.Getenv("PATIENT_PORTAL_BASE_URL")
	if patientPortalBaseURL == "" {
		patientPortalBaseURL = "https://surgassists-connect.online"
	}

	qrCodeURL := fmt.Sprintf("%s/qr-display/%s", patientPortalBaseURL, patientID)
	subject := "Here is your QR code to start the AR app"
	emailBody := fmt.Sprintf(`Dear Patient,

Congratulations! Your document has been successfully signed and completed.

You can now access your personalized AR experience by using the QR code generator below:

%s

Simply visit the link above to generate your unique QR code, then scan it with the AR app to begin your immersive medical experience.

Thank you for completing the signing process.

Best regards,
SurgAssists Team`, qrCodeURL)

	log.Printf("Sending QR code email to: %s", email)
	if err := sendEmail(email, subject, emailBody); err != nil {
		log.Printf("Failed to send QR code email to %s: %v", email, err)
		log.Printf("=== END DOCUMENSO WEBHOOK ===")
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to send QR code email",
		})
	}

	log.Printf("QR code email sent successfully to: %s", email)

	// Update Appwrite document with COMPLETED status and DocumentDataID
	log.Printf("Updating Appwrite document status to COMPLETED and saving DocumentDataID")
	if err := updateAppwriteDocumentWithDataID(patientID, "COMPLETED", documentDataId); err != nil {
		log.Printf("Failed to update document status for ID %s: %v", patientID, err)
		log.Printf("=== END DOCUMENSO WEBHOOK ===")
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to update document status",
		})
	}

	log.Printf("Document updated successfully - Status: COMPLETED, DocumentDataID: %s", documentDataId)
	log.Printf("=== END DOCUMENSO WEBHOOK ===")

	return c.JSON(fiber.Map{
		"message":          "Documenso document completed successfully",
		"patient_id":       patientID,
		"email":            email,
		"document_data_id": documentDataId,
		"status":           "COMPLETED",
	})

}

// getAppwriteDocumentByEmail searches for a document in Appwrite by email address
func getAppwriteDocumentByEmail(email string) (string, error) {
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")
	consentCollection := os.Getenv("CONSENT_COLLECTION")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" || appwriteDatabaseID == "" || consentCollection == "" {
		return "", fmt.Errorf("appwrite configuration missing")
	}

	// Build the URL with query to search by email
	// Try different query formats for Appwrite
	requestURL := fmt.Sprintf("%s/databases/%s/collections/%s/documents",
		appwriteEndpoint, appwriteDatabaseID, consentCollection)

	log.Printf("Appwrite search URL: %s", requestURL)
	log.Printf("Searching for email: %s", email)

	// Create HTTP request
	req, err := http.NewRequest("GET", requestURL, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create appwrite request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send appwrite request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		// Read the response body for error details
		bodyBytes, _ := io.ReadAll(resp.Body)
		log.Printf("Appwrite error response: %s", string(bodyBytes))
		return "", fmt.Errorf("appwrite returned status: %d, response: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse the response
	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return "", fmt.Errorf("failed to decode appwrite response: %v", err)
	}

	// Check if documents were found
	documents, exists := result["documents"]
	if !exists {
		return "", fmt.Errorf("no documents field in response")
	}

	documentsArray, ok := documents.([]interface{})
	if !ok || len(documentsArray) == 0 {
		return "", fmt.Errorf("no documents found in collection")
	}

	log.Printf("Found %d documents in collection, searching for email: %s", len(documentsArray), email)

	// Search through all documents for matching email
	for i, doc := range documentsArray {
		docMap, ok := doc.(map[string]interface{})
		if !ok {
			log.Printf("Document %d has invalid format", i)
			continue
		}

		// Check if this document has the matching email
		if emailField, exists := docMap["Email"]; exists {
			if emailStr, ok := emailField.(string); ok {
				log.Printf("Document %d has email: %s", i, emailStr)
				if emailStr == email {
					// Found matching document, get its ID
					if docID, exists := docMap["$id"]; exists {
						if docIDStr, ok := docID.(string); ok {
							log.Printf("Found matching document with ID: %s", docIDStr)
							return docIDStr, nil
						}
					}
				}
			}
		}
	}

	return "", fmt.Errorf("no document found with email: %s", email)
}

// updateAppwriteDocumentWithDataID updates both status and DocumentDataID in Appwrite
func updateAppwriteDocumentWithDataID(documentID, status, documentDataID string) error {
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")
	consentCollection := os.Getenv("CONSENT_COLLECTION")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" || appwriteDatabaseID == "" || consentCollection == "" {
		return fmt.Errorf("appwrite configuration missing")
	}

	// Build the URL
	url := fmt.Sprintf("%s/databases/%s/collections/%s/documents/%s",
		appwriteEndpoint, appwriteDatabaseID, consentCollection, documentID)

	log.Printf("Appwrite update URL: %s", url)

	// Create the update payload
	updateData := map[string]interface{}{
		"Status":         status,
		"DocumentDataID": documentDataID,
	}

	// Convert to JSON
	jsonData, err := json.Marshal(updateData)
	if err != nil {
		return fmt.Errorf("failed to marshal update data: %v", err)
	}

	log.Printf("Update payload: %s", string(jsonData))

	// Create HTTP request
	req, err := http.NewRequest("PATCH", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create appwrite request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send appwrite request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		// Read the response body for error details
		bodyBytes, _ := io.ReadAll(resp.Body)
		log.Printf("Appwrite update error response: %s", string(bodyBytes))
		return fmt.Errorf("appwrite returned status: %d, response: %s", resp.StatusCode, string(bodyBytes))
	}

	log.Printf("Successfully updated document %s with status: %s and DocumentDataID: %s", documentID, status, documentDataID)
	return nil
}

func AdminGetListUsers(c *fiber.Ctx) error {
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" {
		log.Printf("Appwrite configuration missing for admin users list")
		return c.Status(500).JSON(fiber.Map{
			"error": "Appwrite configuration missing",
		})
	}

	// Build the Appwrite Users API URL
	url := fmt.Sprintf("%s/users", appwriteEndpoint)

	log.Printf("Fetching users from Appwrite: %s", url)

	// Create HTTP request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.Printf("Failed to create Appwrite users request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create request",
		})
	}

	// Set headers for Appwrite Server API
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Failed to send Appwrite users request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to fetch users",
		})
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		log.Printf("Appwrite users API error - Status: %d, Response: %s", resp.StatusCode, string(bodyBytes))
		return c.Status(resp.StatusCode).JSON(fiber.Map{
			"error":   "Appwrite API error",
			"details": string(bodyBytes),
		})
	}

	// Parse the response
	var usersResponse map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&usersResponse); err != nil {
		log.Printf("Failed to decode Appwrite users response: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to parse users response",
		})
	}

	// Log success
	if users, exists := usersResponse["users"]; exists {
		if usersArray, ok := users.([]interface{}); ok {
			log.Printf("Successfully fetched %d users from Appwrite", len(usersArray))
		}
	}

	// Return the users data
	return c.JSON(fiber.Map{
		"message": "Users fetched successfully",
		"data":    usersResponse,
	})
}

func AdminCreateUser(c *fiber.Ctx) error {
	// Parse the JSON body
	var userData map[string]interface{}
	if err := c.BodyParser(&userData); err != nil {
		log.Printf("Error parsing request body: %v", err)
		return c.Status(400).JSON(fiber.Map{
			"error": "Invalid JSON payload",
		})
	}

	// Log the received data
	log.Printf("Received user data:")
	log.Printf("  Email: %s", userData["email"])
	log.Printf("  Password: %s", userData["password"])
	log.Printf("  Name: %s", userData["name"])

	// Validate required fields
	email, emailOk := userData["email"].(string)
	password, passwordOk := userData["password"].(string)
	name, nameOk := userData["name"].(string)

	if !emailOk || !passwordOk || !nameOk || email == "" || password == "" || name == "" {
		log.Printf("Missing or invalid required fields")
		return c.Status(400).JSON(fiber.Map{
			"error": "Email, password, and name are required",
		})
	}

	// Get Appwrite configuration
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" {
		log.Printf("Appwrite configuration missing for user creation")
		return c.Status(500).JSON(fiber.Map{
			"error": "Appwrite configuration missing",
		})
	}

	// Build the Appwrite Users API URL
	url := fmt.Sprintf("%s/users", appwriteEndpoint)

	// Create user payload for Appwrite
	userPayload := map[string]interface{}{
		"userId":   "unique()", // Let Appwrite generate unique ID
		"email":    email,
		"password": password,
		"name":     name,
	}

	// Convert to JSON
	jsonData, err := json.Marshal(userPayload)
	if err != nil {
		log.Printf("Failed to marshal user data: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to prepare user data",
		})
	}

	log.Printf("Creating user in Appwrite: %s", url)
	log.Printf("User payload: %s", string(jsonData))

	// Create HTTP request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		log.Printf("Failed to create Appwrite user request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create request",
		})
	}

	// Set headers for Appwrite Server API
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Failed to send Appwrite user creation request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create user",
		})
	}
	defer resp.Body.Close()

	// Read response body
	bodyBytes, _ := io.ReadAll(resp.Body)

	// Check response status
	if resp.StatusCode != http.StatusCreated {
		log.Printf("Appwrite user creation error - Status: %d, Response: %s", resp.StatusCode, string(bodyBytes))
		return c.Status(resp.StatusCode).JSON(fiber.Map{
			"error":   "Failed to create user in Appwrite",
			"details": string(bodyBytes),
		})
	}

	// Parse the successful response
	var createdUser map[string]interface{}
	if err := json.Unmarshal(bodyBytes, &createdUser); err != nil {
		log.Printf("Failed to parse created user response: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to parse user creation response",
		})
	}

	log.Printf("User created successfully in Appwrite with ID: %s", createdUser["$id"])

	// Return success response
	return c.Status(201).JSON(fiber.Map{
		"message": "User created successfully",
		"user":    createdUser,
	})
}

func AdminGetUserInfo(c *fiber.Ctx) error {
	// Get the user ID from the URL parameter
	userID := c.Params("id")

	if userID == "" {
		log.Printf("Missing user ID in URL")
		return c.Status(400).JSON(fiber.Map{
			"error": "User ID is required",
		})
	}

	log.Printf("Getting user info for ID: %s", userID)

	// Get Appwrite configuration
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" || appwriteDatabaseID == "" {
		log.Printf("Appwrite configuration missing for user info")
		return c.Status(500).JSON(fiber.Map{
			"error": "Appwrite configuration missing",
		})
	}

	// Profile collection ID
	profileCollection := "6852be26001922ea0c85"

	// Build the URL to search Profile collection by UserID attribute
	url := fmt.Sprintf("%s/databases/%s/collections/%s/documents",
		appwriteEndpoint, appwriteDatabaseID, profileCollection)

	log.Printf("Searching user profile by UserID: %s", userID)
	log.Printf("Profile collection URL: %s", url)

	// Create HTTP request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.Printf("Failed to create Appwrite profile request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create request",
		})
	}

	// Set headers for Appwrite Server API
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Failed to send Appwrite profile request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to fetch user profile",
		})
	}
	defer resp.Body.Close()

	// Read response body
	bodyBytes, _ := io.ReadAll(resp.Body)

	// Check response status
	if resp.StatusCode != http.StatusOK {
		log.Printf("Appwrite profile API error - Status: %d, Response: %s", resp.StatusCode, string(bodyBytes))
		return c.Status(resp.StatusCode).JSON(fiber.Map{
			"error":   "Failed to fetch user profiles",
			"details": string(bodyBytes),
		})
	}

	// Parse the response
	var profilesResponse map[string]interface{}
	if err := json.Unmarshal(bodyBytes, &profilesResponse); err != nil {
		log.Printf("Failed to parse profiles response: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to parse profiles response",
		})
	}

	// Check if documents were found
	documents, exists := profilesResponse["documents"]
	if !exists {
		return c.Status(500).JSON(fiber.Map{
			"error": "No documents field in response",
		})
	}

	documentsArray, ok := documents.([]interface{})
	if !ok || len(documentsArray) == 0 {
		log.Printf("No profiles found in collection")
		return c.Status(404).JSON(fiber.Map{
			"error":   "User profile not found",
			"user_id": userID,
		})
	}

	log.Printf("Found %d profiles in collection, searching for UserID: %s", len(documentsArray), userID)

	// Search through all profiles for matching UserID
	for i, doc := range documentsArray {
		docMap, ok := doc.(map[string]interface{})
		if !ok {
			log.Printf("Profile %d has invalid format", i)
			continue
		}

		// Check if this profile has the matching UserID
		if userIDField, exists := docMap["UserID"]; exists {
			if userIDStr, ok := userIDField.(string); ok {
				log.Printf("Profile %d has UserID: %s", i, userIDStr)
				if userIDStr == userID {
					// Found matching profile
					log.Printf("Found matching profile for UserID: %s", userID)
					return c.JSON(fiber.Map{
						"message": "User profile fetched successfully",
						"user_id": userID,
						"profile": docMap,
					})
				}
			}
		}
	}

	// No matching profile found
	log.Printf("No profile found with UserID: %s", userID)
	return c.Status(404).JSON(fiber.Map{
		"error":   "User profile not found",
		"user_id": userID,
	})
}
