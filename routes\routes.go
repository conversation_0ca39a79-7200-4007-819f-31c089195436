package routes

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"

	"github.com/gofiber/fiber/v2"
)

// PatientConsentRequest represents the incoming JSON payload
type PatientConsentRequest struct {
	ID        string `json:"$id"`
	UserID    string `json:"UserID"`
	Email     string `json:"Email"`
	Status    string `json:"Status"`
	CreatedAt string `json:"$createdAt"`
}

// PostalEmailRequest represents the request structure for Postal API
type PostalEmailRequest struct {
	To        []string `json:"to"`
	From      string   `json:"from"`
	Subject   string   `json:"subject"`
	PlainBody string   `json:"plain_body"`
}

// AppwriteUpdateRequest represents the request structure for updating Appwrite document
type AppwriteUpdateRequest struct {
	Data map[string]interface{} `json:"data"`
}

// getAppwriteDocument retrieves a document from Appwrite database
func getAppwriteDocument(documentID string) (map[string]interface{}, error) {
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	consentCollection := os.Getenv("CONSENT_COLLECTION")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")

	if appwriteKey == "" || appwriteEndpoint == "" || consentCollection == "" {
		return nil, fmt.Errorf("appwrite configuration missing")
	}

	// Use default values if not specified
	if appwriteProjectID == "" {
		appwriteProjectID = "default"
	}
	if appwriteDatabaseID == "" {
		appwriteDatabaseID = "default"
	}

	// Build the URL
	url := fmt.Sprintf("%s/databases/%s/collections/%s/documents/%s",
		appwriteEndpoint, appwriteDatabaseID, consentCollection, documentID)

	// Create HTTP request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create appwrite request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Log the request details for debugging
	log.Printf("=== APPWRITE GET REQUEST ===")
	log.Printf("Full URL: %s", url)
	log.Printf("Method: GET")
	log.Printf("Headers:")
	log.Printf("  Content-Type: application/json")
	log.Printf("  X-Appwrite-Project: %s", appwriteProjectID)
	log.Printf("  X-Appwrite-Key: %s", appwriteKey)
	log.Printf("============================")

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get appwrite document: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	var responseBody map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&responseBody); err != nil {
		return nil, fmt.Errorf("failed to decode response: %v", err)
	}

	log.Printf("Appwrite response status: %d", resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		log.Printf("Appwrite error response: %v", responseBody)
		return nil, fmt.Errorf("appwrite service returned status: %d", resp.StatusCode)
	}

	log.Printf("Appwrite document retrieved successfully for ID: %s", documentID)
	return responseBody, nil
}

// updateAppwriteDocument updates the document status in Appwrite database
func updateAppwriteDocument(documentID string) error {
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	consentCollection := os.Getenv("CONSENT_COLLECTION")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")

	if appwriteKey == "" || appwriteEndpoint == "" || consentCollection == "" {
		return fmt.Errorf("appwrite configuration missing")
	}

	// Use default values if not specified
	if appwriteProjectID == "" {
		appwriteProjectID = "default"
	}
	if appwriteDatabaseID == "" {
		appwriteDatabaseID = "default"
	}

	// Create the update request
	updateRequest := AppwriteUpdateRequest{
		Data: map[string]interface{}{
			"Status": "Email Sent",
		},
	}

	// Convert to JSON
	jsonData, err := json.Marshal(updateRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal appwrite update request: %v", err)
	}

	// Build the URL
	url := fmt.Sprintf("%s/databases/%s/collections/%s/documents/%s",
		appwriteEndpoint, appwriteDatabaseID, consentCollection, documentID)

	// Create HTTP request
	req, err := http.NewRequest("PATCH", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create appwrite request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Log the request details for debugging
	log.Printf("=== APPWRITE UPDATE REQUEST ===")
	log.Printf("Full URL: %s", url)
	log.Printf("Method: PATCH")
	log.Printf("Headers:")
	log.Printf("  Content-Type: application/json")
	log.Printf("  X-Appwrite-Project: %s", appwriteProjectID)
	log.Printf("  X-Appwrite-Key: %s", appwriteKey)
	log.Printf("Request Body: %s", string(jsonData))
	log.Printf("===============================")

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to update appwrite document: %v", err)
	}
	defer resp.Body.Close()

	// Read response body for better error debugging
	body, _ := json.Marshal(resp.Body)
	log.Printf("Appwrite response status: %d", resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		log.Printf("Appwrite error response: %s", string(body))
		return fmt.Errorf("appwrite service returned status: %d", resp.StatusCode)
	}

	log.Printf("Appwrite document %s updated successfully with status 'Email Sent'", documentID)
	return nil
}

// sendEmail sends an email using the Postal mail service
func sendEmail(toEmail, patientID string) error {
	mailServerAPIKey := os.Getenv("MAIL_SERVER_API_KEY")
	mailServiceURL := os.Getenv("MAIL_SERVICE_URL")
	patientPortalBaseURL := os.Getenv("PATIENT_PORTAL_BASE_URL")

	if mailServerAPIKey == "" || mailServiceURL == "" {
		return fmt.Errorf("mail server configuration missing")
	}

	// Use default URL if not configured
	if patientPortalBaseURL == "" {
		patientPortalBaseURL = "https://surgassists-connect.online"
	}

	// Create the email content
	subject := "Medical AR App - Content Request"
	body := fmt.Sprintf("We are reaching out via email to request content for a medical AR app.\n\nPlease visit: %s/patient/%s", patientPortalBaseURL, patientID)

	emailRequest := PostalEmailRequest{
		To:        []string{toEmail},
		From:      "<EMAIL>", // You may want to make this configurable
		Subject:   subject,
		PlainBody: body,
	}

	// Convert to JSON
	jsonData, err := json.Marshal(emailRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal email request: %v", err)
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", mailServiceURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Server-API-Key", mailServerAPIKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send email: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("email service returned status: %d", resp.StatusCode)
	}

	log.Printf("Email sent successfully to %s", toEmail)
	return nil
}

func Hello(c *fiber.Ctx) error {

	return c.SendString("Hello, World!")
}

func Allowed(c *fiber.Ctx) error {
	return c.SendString("Successfully authenticated!")
}

func StartPatientConsent(c *fiber.Ctx) error {
	var request PatientConsentRequest

	// Parse the JSON body
	if err := c.BodyParser(&request); err != nil {
		log.Printf("Error parsing request body: %v", err)
		return c.Status(400).JSON(fiber.Map{
			"error": "Invalid JSON payload",
		})
	}

	// Log the received data
	log.Printf("Received patient consent request:")
	log.Printf("  ID: %s", request.ID)
	log.Printf("  UserID: %s", request.UserID)
	log.Printf("  Email: %s", request.Email)
	log.Printf("  Status: %s", request.Status)
	log.Printf("  CreatedAt: %s", request.CreatedAt)

	// Send email to the patient
	if err := sendEmail(request.Email, request.ID); err != nil {
		log.Printf("Failed to send email to %s: %v", request.Email, err)
		return c.Status(500).JSON(fiber.Map{
			"error":   "Failed to send email",
			"message": "Patient consent request received but email sending failed",
			"data":    request,
		})
	}

	log.Printf("Email sent successfully to %s for patient ID: %s", request.Email, request.ID)

	// Update Appwrite document status to "Email Sent"
	if err := updateAppwriteDocument(request.ID); err != nil {
		log.Printf("Failed to update Appwrite document %s: %v", request.ID, err)
		return c.Status(500).JSON(fiber.Map{
			"error":      "Failed to update database",
			"message":    "Email sent successfully but database update failed",
			"data":       request,
			"email_sent": true,
			"db_updated": false,
		})
	}

	log.Printf("Appwrite document updated successfully for patient ID: %s", request.ID)

	return c.JSON(fiber.Map{
		"message":    "Patient consent request received, logged, email sent, and database updated successfully",
		"data":       request,
		"email_sent": true,
		"db_updated": true,
	})
}

func LogStats(c *fiber.Ctx) error {
	return c.SendString("Logging stats...")
}

func PatientConsentViewed(c *fiber.Ctx) error {
	// Get the ID from the URL parameter
	patientID := c.Params("id")

	if patientID == "" {
		log.Printf("Missing patient ID in URL")
		return c.Status(400).JSON(fiber.Map{
			"error": "Patient ID is required",
		})
	}

	log.Printf("Patient consent viewed for ID: %s", patientID)

	// Get the document from Appwrite
	document, err := getAppwriteDocument(patientID)
	if err != nil {
		log.Printf("Failed to retrieve document for ID %s: %v", patientID, err)
		return c.Status(500).JSON(fiber.Map{
			"error":      "Failed to retrieve patient information",
			"patient_id": patientID,
		})
	}

	return c.JSON(fiber.Map{
		"message":    "Patient consent viewed successfully",
		"patient_id": patientID,
		"document":   document,
	})
}
